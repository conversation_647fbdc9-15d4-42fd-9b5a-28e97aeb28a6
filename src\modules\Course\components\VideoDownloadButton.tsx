import React, { useState, useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity, Text } from 'react-native';
import { AppButton, Winicon, showSnackbar, ComponentStatus } from 'wini-mobile-components';
import { ColorThemes } from '../../../assets/skin/colors';
import { TypoSkin } from '../../../assets/skin/typography';
import VideoDownloadManager, { SavedVideo, DownloadProgress } from '../../../utils/VideoDownloadManager';
import OfflineVideoStorage from '../../../utils/OfflineVideoStorage';

interface VideoDownloadButtonProps {
  videoUrl: string;
  lessonId: string;
  lessonName: string;
  courseId: string;
  videoName: string;
  videoIndex?: number;
  style?: any;
  size?: 'small' | 'medium' | 'large';
  onDownloadStart?: () => void;
  onDownloadComplete?: (savedVideo: SavedVideo) => void;
  onDownloadError?: (error: string) => void;
}

const VideoDownloadButton: React.FC<VideoDownloadButtonProps> = ({
  videoUrl,
  lessonId,
  lessonName,
  courseId,
  videoName,
  videoIndex = 0,
  style,
  size = 'medium',
  onDownloadStart,
  onDownloadComplete,
  onDownloadError,
}) => {
  const [downloadStatus, setDownloadStatus] = useState<'none' | 'downloading' | 'completed' | 'paused' | 'failed'>('none');
  const [progress, setProgress] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [downloadId, setDownloadId] = useState<string | null>(null);

  const downloadManager = VideoDownloadManager.getInstance();
  const offlineStorage = OfflineVideoStorage.getInstance();

  // Size configurations
  const sizeConfig = {
    small: { iconSize: 16, buttonSize: 32, fontSize: 12 },
    medium: { iconSize: 20, buttonSize: 40, fontSize: 14 },
    large: { iconSize: 24, buttonSize: 48, fontSize: 16 },
  };

  const config = sizeConfig[size];

  useEffect(() => {
    // Reset state when component mounts or video changes
    setDownloadStatus('none');
    setProgress(0);
    setDownloadId(null);
    setIsLoading(true);

    checkDownloadStatus();
  }, [videoUrl, lessonId]);

  const checkDownloadStatus = async () => {
    try {
      setIsLoading(true);

      // Check if video is already downloaded
      const offlineVideo = await offlineStorage.isVideoAvailableOffline(lessonId, videoUrl);
      if (offlineVideo) {
        setDownloadStatus('completed');
        setProgress(100);
        setIsLoading(false);
        return;
      }

      // Check if video is currently downloading
      const allVideos = await downloadManager.getAllSavedVideos();
      const currentVideo = allVideos.find(v =>
        v.lessonId === lessonId &&
        v.videoUrl === videoUrl
      );

      if (currentVideo) {
        // Verify the download status is still valid
        if (currentVideo.downloadStatus === 'downloading' || currentVideo.downloadStatus === 'paused') {
          // Check if the download task still exists
          const progress = await downloadManager.getDownloadProgress(currentVideo.id);
          if (progress) {
            setDownloadStatus(currentVideo.downloadStatus as any);
            setProgress(currentVideo.progress);
            setDownloadId(currentVideo.id);
          } else {
            // Download task doesn't exist, reset status
            setDownloadStatus('none');
            setProgress(0);
            setDownloadId(null);
          }
        } else {
          setDownloadStatus(currentVideo.downloadStatus as any);
          setProgress(currentVideo.progress);
          setDownloadId(currentVideo.id);
        }
      } else {
        setDownloadStatus('none');
        setProgress(0);
        setDownloadId(null);
      }
    } catch (error) {
      console.error('Error checking download status:', error);
      setDownloadStatus('none');
      setProgress(0);
      setDownloadId(null);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDownload = async () => {
    try {
      if (downloadStatus === 'downloading') {
        // Pause download
        if (downloadId) {
          const success = await downloadManager.pauseDownload(downloadId);
          if (success) {
            setDownloadStatus('paused');
            showSnackbar({
              message: 'Đã tạm dừng tải video',
              status: ComponentStatus.INFOR,
            });
          }
        }
        return;
      }

      if (downloadStatus === 'paused') {
        // Resume download
        if (downloadId) {
          const success = await downloadManager.resumeDownload(downloadId);
          if (success) {
            setDownloadStatus('downloading');
            showSnackbar({
              message: 'Đã tiếp tục tải video',
              status: ComponentStatus.INFOR,
            });
          }
        }
        return;
      }

      if (downloadStatus === 'completed') {
        showSnackbar({
          message: 'Video đã được tải xuống',
          status: ComponentStatus.SUCCSESS,
        });
        return;
      }

      // Start new download
      setDownloadStatus('downloading');
      setProgress(0);
      
      if (onDownloadStart) {
        onDownloadStart();
      }

      const newDownloadId = await downloadManager.startDownload(
        {
          videoUrl,
          lessonName,
          lessonId,
          courseId,
          videoName: videoName || `Video ${videoIndex + 1}`,
        },
        (progressData: DownloadProgress) => {
          setProgress(progressData.progress);
          setDownloadStatus('downloading');
        },
        (savedVideo: SavedVideo) => {
          setDownloadStatus('completed');
          setProgress(100);
          if (onDownloadComplete) {
            onDownloadComplete(savedVideo);
          }
        },
        (error: string) => {
          setDownloadStatus('failed');
          setProgress(0);
          if (onDownloadError) {
            onDownloadError(error);
          }
        }
      );

      if (newDownloadId) {
        setDownloadId(newDownloadId);
      } else {
        setDownloadStatus('failed');
      }
    } catch (error) {
      console.error('Error handling download:', error);
      setDownloadStatus('failed');
      showSnackbar({
        message: 'Có lỗi xảy ra khi tải video',
        status: ComponentStatus.ERROR,
      });
    }
  };

  const handleCancel = async () => {
    if (downloadId && (downloadStatus === 'downloading' || downloadStatus === 'paused')) {
      try {
        const success = await downloadManager.cancelDownload(downloadId);
        if (success) {
          setDownloadStatus('none');
          setProgress(0);
          setDownloadId(null);
          showSnackbar({
            message: 'Đã hủy tải video',
            status: ComponentStatus.INFOR,
          });
        }
      } catch (error) {
        console.error('Error canceling download:', error);
      }
    }
  };

  const getButtonIcon = () => {
    if (isLoading) {
      return 'fill/arrows/data-download';
    }

    switch (downloadStatus) {
      case 'downloading':
        return 'outline/sound/media-pause';
      case 'paused':
        return 'fill/multimedia/button-play';
      case 'completed':
        return 'fill/user interface/check';
      case 'failed':
        return 'outline/arrows/refresh';
      default:
        return 'fill/arrows/data-download';
    }
  };

  const getButtonColor = () => {
    switch (downloadStatus) {
      case 'downloading':
        return ColorThemes.light.Warning_Color_Main;
      case 'paused':
        return ColorThemes.light.Info_Color_Main;
      case 'completed':
        return ColorThemes.light.Success_Color_Main;
      case 'failed':
        return ColorThemes.light.Error_Color_Main;
      default:
        return ColorThemes.light.Primary_Color_Main;
    }
  };



  if (isLoading) {
    return (
      <View style={[styles.container, style]}>
        <TouchableOpacity 
          style={[
            styles.button, 
            { 
              width: config.buttonSize, 
              height: config.buttonSize,
              backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
            }
          ]}
          disabled
        >
          <Winicon
            src="outline/user interface/loading"
            size={config.iconSize}
            color={ColorThemes.light.Neutral_Text_Color_Subtitle}
          />
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      {/* Circular Progress Ring */}
      {downloadStatus === 'downloading' && (
        <View style={[styles.progressRing, { width: config.buttonSize + 8, height: config.buttonSize + 8 }]}>
          {/* Background circle */}
          <View
            style={[
              styles.progressCircleBackground,
              {
                width: config.buttonSize + 8,
                height: config.buttonSize + 8,
                borderRadius: (config.buttonSize + 8) / 2,
              }
            ]}
          />
          {/* Progress circle */}
          <View
            style={[
              styles.progressCircle,
              {
                width: config.buttonSize + 8,
                height: config.buttonSize + 8,
                borderRadius: (config.buttonSize + 8) / 2,
                transform: [{ rotate: `${(progress * 3.6) - 90}deg` }]
              }
            ]}
          />
        </View>
      )}

      <TouchableOpacity
        style={[
          styles.button,
          {
            width: config.buttonSize,
            height: config.buttonSize,
            backgroundColor: downloadStatus === 'completed'
              ? ColorThemes.light.Success_Color_Background_color
              : ColorThemes.light.Neutral_Background_Color_Main,
              borderWidth: 1,
              borderColor: downloadStatus === 'downloading'
                ? ColorThemes.light.Primary_Color_Main
                : ColorThemes.light.Neutral_Border_Color_Main,
          }
        ]}
        onPress={handleDownload}
        disabled={isLoading}
      >
        <Winicon
          src={getButtonIcon()}
          size={config.iconSize}
          color={getButtonColor()}
        />
      </TouchableOpacity>

      {/* Progress text overlay */}
      {downloadStatus === 'downloading' && (
        <View style={styles.progressTextOverlay}>
          <Text style={[styles.progressTextSmall, { fontSize: config.fontSize * 0.6 }]}>
            {progress}%
          </Text>
        </View>
      )}

      {/* Cancel button for downloading/paused */}
      {(downloadStatus === 'downloading' || downloadStatus === 'paused') && (
        <TouchableOpacity
          style={[styles.cancelButton, { width: config.buttonSize * 0.6, height: config.buttonSize * 0.6 }]}
          onPress={handleCancel}
        >
          <Winicon
            src="outline/user interface/xmark"
            size={config.iconSize * 0.7}
            color={ColorThemes.light.Error_Color_Main}
          />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  button: {
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  progressRing: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
  },
  progressCircleBackground: {
    position: 'absolute',
    borderWidth: 2,
    borderColor: ColorThemes.light.Neutral_Border_Color_Main,
  },
  progressCircle: {
    position: 'absolute',
    borderWidth: 2,
    borderColor: 'transparent',
    borderTopColor: ColorThemes.light.Primary_Color_Main,
    borderRightColor: ColorThemes.light.Primary_Color_Main,
  },
  progressTextOverlay: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -10 }, { translateY: -6 }],
  },
  progressTextSmall: {
    textAlign: 'center',
    color: ColorThemes.light.Primary_Color_Main,
    fontWeight: '600',
  },
  progressContainer: {
    position: 'absolute',
    bottom: -20,
    left: 0,
    right: 0,
    height: 12,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
    borderRadius: 6,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: ColorThemes.light.Neutral_Border_Color_Main,
  },
  progressBar: {
    height: '100%',
    backgroundColor: ColorThemes.light.Primary_Color_Main,
    borderRadius: 5,
  },
  progressText: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    textAlign: 'center',
    lineHeight: 12,
    color: ColorThemes.light.Neutral_Text_Color_Title,
    fontWeight: '600',
    fontSize: 10,
  },
  cancelButton: {
    position: 'absolute',
    top: -5,
    right: -5,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: ColorThemes.light.Error_Color_Main,
  },
});

export default VideoDownloadButton;
